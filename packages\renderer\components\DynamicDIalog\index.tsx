import {
  defineComponent,
  reactive,
  ref,
  toRef,
  useModel,
  computed,
  watch,
  onMounted,
  toRaw
} from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import { useBem, useTableCommonMenu } from '@/renderer/hooks'
import $styles from './index.module.scss'
import TableTool, { isAddOrInsertType, OpType } from '@/renderer/components/TableTool'
import { WuiForm, WuiMessage, WuiScrollbar } from '@wuk/wui'
import type {
  FormField,
  ButtonFormField,
  TableColumn,
  TableRowData,
  NestedDialogContext,
  NestedPathNode
} from './types'
import { useNestedDialog } from './useNestedDialog'
import { convertType } from '@/renderer/utils/common'
import { createDefaultFormData, getResolvedNestedDefaultValues, processDialogData } from './utils'
import RangeForm from './components/RangeForm/index.tsx'
import ColorSelect from '@/renderer/components/ColorSelect/index.tsx'
import Matrix from './components/Matrix/index.tsx'

const DynamicDialog = defineComponent({
  name: 'DynamicDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => ({})
    },
    apiCallbacks: {
      type: Object,
      default: () => ({})
    },
    events: {
      type: Object,
      default: () => ({})
    },
    // 父级嵌套路径，用于多层嵌套
    parentNestedPath: {
      type: Array as () => NestedPathNode[],
      default: () => []
    },
    // 当前弹窗的key
    dialogKey: {
      type: String,
      default: 'main'
    }
  },
  emits: ['update:visible', 'ok', 'cancel', 'form-change', 'table-change'],
  setup(props, { emit }) {
    const visible = useModel(props, 'visible')
    const { b, e } = useBem('dynamic-dialog', $styles)
    const formRef = ref<InstanceType<typeof WuiForm>>()
    const nestedDialogManager = useNestedDialog()

    // 响应式数据模型
    const model = reactive({
      form: { ...(props.config.form?.data || {}) },
      table: [...(props.config.table?.data || [])]
    })

    let originList = JSON.parse(JSON.stringify(model.table))

    // 当前弹窗的嵌套路径
    const currentNestedPath = computed<NestedPathNode[]>(() => {
      const parentPath = props.parentNestedPath || []
      const currentNode = {
        dialogKey: props.dialogKey,
        formData: model.form,
        tableData: model.table
      }
      return [...parentPath, currentNode]
    })

    // 构建上下文的辅助函数
    const buildContext = (
      selectedRow?: TableRowData,
      selectedRowIndex?: number
    ): NestedDialogContext => {
      const nestedPath = currentNestedPath.value
      const parentNode = nestedPath[nestedPath.length - 2] // 父级节点
      nestedPath[nestedPath.length - 1].selectedRow = selectedRow
      nestedPath[nestedPath.length - 1].selectedRowIndex = selectedRowIndex

      return {
        ...(props.config.context || {}),
        nestedPath,
        currentDialog: {
          dialogKey: props.dialogKey,
          level: nestedPath.length - 1,
          currentRowIndex: selectedRowIndex,
          currentRow: selectedRow
        },
        key: props.config.key,
        parentFormData: parentNode?.formData,
        parentTableData: parentNode?.tableData,
        parentRowIndex: parentNode?.selectedRowIndex,
        parentRow: parentNode?.selectedRow,
        selectedRow,
        selectedRowIndex
      }
    }

    // 计算属性
    const formColumns = computed(() => props.config.form?.columns || [])
    const tableColumns = computed(() => props.config.table?.columns || [])
    const showTableOperations = computed(() => props.config.table?.showOperation !== false)
    const tableOperations = computed(
      () => props.config.table?.operations || ['add', 'insert', 'modify', 'delete']
    )

    // 标记是否已经初始化，避免重复重置用户输入
    const isInitialized = ref(false)

    // 监听配置变化，仅在初始化时更新数据模型
    watch(
      () => props.config,
      newConfig => {
        // 只在第一次或者弹窗重新打开时初始化数据
        if (!isInitialized.value || !visible.value) {
          if (newConfig.form?.data) {
            Object.assign(model.form, newConfig.form.data)
          }
          if (newConfig.table?.data) {
            model.table.splice(0, model.table.length, ...newConfig.table.data)
            originList = JSON.parse(JSON.stringify(model.table))
          }
          isInitialized.value = true
        }
      },
      { deep: true, immediate: true }
    )

    // 监听弹窗关闭，重置初始化标记
    watch(visible, newVisible => {
      if (!newVisible) {
        isInitialized.value = false
      }
    })

    // 监听表单数据变化，实时触发回调
    watch(
      () => model.form,
      newFormData => {
        if (isInitialized.value && visible.value) {
          emit('form-change', newFormData)
        }
      },
      { deep: true }
    )

    // 创建新行
    const createRow = (rowType: 'add' | 'insert' = 'add'): TableRowData => {
      const newRow: TableRowData = {
        flag: true,
        row_type: rowType
      }
      // 根据表格列配置初始化默认值
      tableColumns.value.forEach((column: TableColumn) => {
        newRow[column.name] = ''
      })
      return newRow
    }

    // 处理嵌套弹窗
    const handleNestedDialog = async (
      dialogKey: string,
      selectedRow?: TableRowData,
      selectedRowIndex?: number
    ) => {
      const nestedConfig = props.config.nestedDialogs?.[dialogKey]
      if (!nestedConfig) {
        return
      }

      if (selectedRow?.isNotSave) {
        WuiMessage({
          message: 'Please save the data first',
          type: 'warning',
          offset: 80
        })
        return
      }

      const context = buildContext(selectedRow, selectedRowIndex)

      const finalConfig = {
        ...nestedConfig,
        context: {
          ...(props.config.context || {}),
          ...(nestedConfig.context || {}),
          ...context
        }
      }

      if (typeof nestedConfig.title === 'function') {
        finalConfig.title = nestedConfig.title(context)
      }

      const nestedDefaults = nestedConfig.defaultValues
      if (nestedDefaults) {
        const resolvedDefaults = getResolvedNestedDefaultValues(nestedDefaults, context)

        nestedConfig.formTemplate?.columns.forEach((column: any) => {
          if (column.type === 'select' && typeof column.options === 'function') {
            column.options = column.options(context)
          }
        })

        nestedConfig.tableTemplate?.columns.forEach((column: any) => {
          if (column.type === 'select' && typeof column.options === 'function') {
            column.options = column.options(context)
          }
        })

        // 创建表单配置
        if (nestedConfig.formTemplate) {
          finalConfig.form = {
            data: {
              ...createDefaultFormData(nestedConfig.formTemplate.columns),
              ...(resolvedDefaults.form || {})
            },
            columns: nestedConfig.formTemplate.columns,
            rules: nestedConfig.formTemplate.rules
          }
        }

        // 创建表格配置
        if (nestedConfig.tableTemplate) {
          finalConfig.table = {
            data: resolvedDefaults.table || [],
            columns: nestedConfig.tableTemplate.columns,
            height: nestedConfig.tableTemplate.height,
            showOperation: nestedConfig.tableTemplate.showOperation,
            operations: nestedConfig.tableTemplate.operations
          }
        }
      }

      nestedDialogManager.openDialog(dialogKey, finalConfig)
    }

    // 表格右键菜单
    const menuItems = computed(() => {
      const items = []
      if (tableOperations.value.includes('add')) items.push({ key: 'addKey', label: 'Add' })
      if (tableOperations.value.includes('insert'))
        items.push({ key: 'insertKey', label: 'Insert' })
      if (tableOperations.value.includes('modify'))
        items.push({ key: 'modifyKey', label: 'Modify' })
      if (tableOperations.value.includes('delete'))
        items.push({ key: 'deleteKey', label: 'Delete' })
      // 支持嵌套弹窗
      if (props.config.nestedDialogs) {
        Object.keys(props.config.nestedDialogs).forEach(key => {
          items.push({ key: `nested_${key}`, label: key })
        })
      }
      return items
    })

    const { handleRowMenu, updateMenuItems } = useTableCommonMenu(
      toRef(model, 'table'),
      async (key, ...args) => {
        const { row, rowIndex } = args[0]
        const keyStr = String(key)

        if (keyStr.startsWith('nested_')) {
          const dialogKey = keyStr.replace('nested_', '')
          await handleNestedDialog(dialogKey, row, rowIndex)
          return
        }

        switch (key) {
          case 'addKey':
            model.table.push(createRow('add'))
            break
          case 'deleteKey':
            handleOp('delete', row, rowIndex)
            break
          case 'modifyKey':
            handleOp('edit', row, rowIndex)
            break
          case 'insertKey':
            model.table.splice(rowIndex + 1, 0, createRow('insert'))
            break
        }
      },
      [1],
      menuItems.value
    )

    watch(
      menuItems,
      newMenuItems => {
        updateMenuItems(newMenuItems)
      },
      { immediate: true }
    )
    const handleOp = (op: OpType, row: any | undefined, index: number) => {
      if (!row) return
      switch (op) {
        case 'edit':
          row.flag = true
          break
        case 'delete':
          handleDelete(index)
          break
        case 'select':
          handleSelect(row, index)
          row.row_type = '*'
          row.flag = false
          break
        case 'cancel':
          handleOpCancel(row, index)
          break
      }
    }
    const handleOpCancel = (row: any, index: number) => {
      if (isAddOrInsertType(row.row_type)) {
        model.table.splice(index, 1)
        return
      }
      model.table.splice(index, 1, { ...originList[index], flag: false, row_type: '*' })
    }
    const handleSelect = async (row: TableRowData, index: number) => {
      let res: boolean | undefined = true
      const { row_type, flag, ...params } = row

      // 构建上下文
      const context = buildContext(row, index)

      // 优先使用嵌套路径精确更新
      if (props.apiCallbacks?.onNestedUpdate && context.nestedPath) {
        if (row.row_type === 'add') {
          res = await props.apiCallbacks.onNestedUpdate(
            { type: 'tableAdd', data: params },
            context.nestedPath,
            context
          )
        } else if (row.row_type === 'edit') {
          res = await props.apiCallbacks.onNestedUpdate(
            { type: 'tableRow', data: params, index },
            context.nestedPath,
            context
          )
        }
      } else {
        // 兼容旧的单行操作
        if (row.row_type === 'add' && props.apiCallbacks?.onTableRowAdd) {
          res = await props.apiCallbacks.onTableRowAdd(params, context)
        } else if (row.row_type === 'edit' && props.apiCallbacks?.onTableRowUpdate) {
          res = await props.apiCallbacks.onTableRowUpdate(params, index, context)
        }
      }

      if (res) {
        row.row_type = '*'
        row.flag = false
        originList = JSON.parse(JSON.stringify(model.table))
        emit('table-change', model.table)
      }
    }

    const handleDelete = async (index: number) => {
      let res: boolean | undefined = true

      // 构建上下文
      const context = buildContext(undefined, index)

      // 优先使用嵌套路径精确更新
      if (props.apiCallbacks?.onNestedUpdate && context.nestedPath) {
        res = await props.apiCallbacks.onNestedUpdate(
          { type: 'tableDelete', index },
          context.nestedPath,
          context
        )
      } else if (props.apiCallbacks?.onTableRowDelete) {
        // 兼容旧的单行操作
        res = await props.apiCallbacks.onTableRowDelete(index, context)
      }

      if (res) {
        model.table.splice(index, 1)
        emit('table-change', model.table)
      }
    }

    const handleOk = async () => {
      // 构建上下文
      const context = buildContext()

      // 1. 优先使用大接口模式
      if (props.apiCallbacks?.onBulkOperation) {
        const allDialogData = collectAllDialogData()
        const success = await props.apiCallbacks.onBulkOperation(
          {
            type: 'save',
            allDialogData
          },
          context
        )
        if (!success) return
      }
      // 2. 其次使用整体数据保存
      else if (props.apiCallbacks?.onDataSave) {
        const allData = processDialogData(model.form, model.table)
        const success = await props.apiCallbacks.onDataSave(allData, context)
        if (!success) return
      }
      // 3. 兼容旧的表单提交方式
      else if (props.apiCallbacks?.onFormSubmit) {
        const success = await props.apiCallbacks.onFormSubmit(toRaw(model.form), context)
        if (!success) return
      }

      visible.value = false
      const processedData = processDialogData(model.form, model.table)
      emit('ok', processedData)
    }

    // 收集所有弹窗数据的方法
    const collectAllDialogData = () => {
      const allDialogData: Record<string, any> = {}

      // 当前弹窗数据
      const currentProcessedData = processDialogData(model.form, model.table)
      allDialogData[props.dialogKey] = {
        ...currentProcessedData,
        level: currentNestedPath.value.length - 1
      }

      // 收集所有嵌套弹窗数据
      Object.entries(nestedDialogManager.dialogs).forEach(([key, dialogState]) => {
        if (dialogState.visible && dialogState.config.form && dialogState.config.table) {
          const nestedProcessedData = processDialogData(
            dialogState.config.form.data,
            dialogState.config.table.data
          )
          allDialogData[key] = {
            ...nestedProcessedData,
            level: currentNestedPath.value.length // 嵌套弹窗的层级
          }
        }
      })

      return allDialogData
    }

    const handleCancel = () => {
      visible.value = false
      emit('cancel')
    }

    // 处理表单按钮点击
    const handleButtonClick = async (buttonField: ButtonFormField) => {
      let dialogKey = buttonField.nestedDialogKey

      // 如果配置了动态弹窗，根据依赖字段的值来决定打开哪个弹窗
      if (buttonField.dynamicNestedDialogs) {
        const { dependsOn, mapping } = buttonField.dynamicNestedDialogs
        const dependentValue = model.form[dependsOn]

        if (dependentValue && mapping[dependentValue]) {
          dialogKey = mapping[dependentValue]
        } else {
          WuiMessage({
            message: 'Please select data',
            type: 'warning',
            offset: 80
          })
          return
        }
      }

      if (dialogKey) {
        await handleNestedDialog(dialogKey, undefined, undefined)
      }
    }

    const renderFormItem = (item: any) => {
      const formItemProps = {
        label: item.label,
        prop: item.name,
        class: item.class,
        labelWidth: item.labelWidth,
        style: item.style
      }
      switch (item.type) {
        case 'input':
          return (
            <wui-form-item {...formItemProps}>
              <wui-input
                v-model={model.form[item.name]}
                placeholder={item.placeholder}
                type={item.isArea ? 'textarea' : ''}
              />
            </wui-form-item>
          )
        case 'select-input':
          return (
            <wui-form-item {...formItemProps}>
              <wui-select
                v-model={model.form[item.name].select}
                placeholder={item.placeholder}
                style={{ marginRight: '10px', width: '290px' }}>
                {item.options.map((option: any) => (
                  <wui-option value={option.value} label={option.label} />
                ))}
              </wui-select>
              <wui-input
                v-model={model.form[item.name].input}
                placeholder={item.placeholder}
                type={item.isArea ? 'textarea' : ''}
              />
            </wui-form-item>
          )
        case 'select':
          return (
            <wui-form-item {...formItemProps}>
              <wui-select v-model={model.form[item.name]} placeholder={item.placeholder}>
                {item.options.map((option: any) => (
                  <wui-option value={option.value} label={option.label} />
                ))}
              </wui-select>
            </wui-form-item>
          )
        case 'button':
          return (
            <wui-form-item {...formItemProps}>
              <wui-button
                type={item.buttonType || 'default'}
                onClick={() => handleButtonClick(item)}>
                {item.label}
              </wui-button>
            </wui-form-item>
          )
        case 'span':
          return (
            <wui-form-item {...formItemProps}>
              <span>{model.form[item.name]}</span>
            </wui-form-item>
          )
        case 'range':
          return (
            <wui-form-item {...formItemProps}>
              <RangeForm
                v-model:modelValue={model.form[item.name]}
                placeholder={item.placeholder}
                showLabel={item.showLabel}
              />
            </wui-form-item>
          )
        case 'colorSelect':
          return (
            <wui-form-item {...formItemProps}>
              <ColorSelect v-model={model.form[item.name]} placeholder={item.placeholder} />
            </wui-form-item>
          )
        case 'matrix':
          return (
            <wui-form-item {...formItemProps}>
              <Matrix v-model:modelValue={model.form[item.name]} />
            </wui-form-item>
          )
        default:
          return null
      }
    }

    const renderTableColumn = (item: any) => {
      switch (item.type) {
        case 'input':
          return (
            <wui-table-column align='center' label={item.label} prop={item.name} width={item.width}>
              {{
                default: ({ row, $index }: any) =>
                  row.flag ? (
                    <wui-input v-model={model.table[$index][item.name]} />
                  ) : (
                    model.table[$index][item.name]
                  )
              }}
            </wui-table-column>
          )
        case 'select':
          return (
            <wui-table-column align='center' label={item.label} prop={item.name} width={item.width}>
              {{
                default: ({ row, $index }: any) =>
                  row.flag ? (
                    <wui-select v-model={model.table[$index][item.name]}>
                      {item.options.map((option: any) => (
                        <wui-option value={option.value} label={option.label} />
                      ))}
                    </wui-select>
                  ) : (
                    convertType(item.options, model.table[$index][item.name])
                  )
              }}
            </wui-table-column>
          )
        default:
          return null
      }
    }

    return () => {
      return (
        <>
          <MyDialog
            width={props.config.width || '800px'}
            v-model={visible.value}
            title={props.config.title}
            contentStyle={{ padding: '0' }}
            onOk={handleOk}>
            <WuiScrollbar class={b()}>
              <div class={[e('body'), 'cfg-setup']}>
                {/* 表单部分 */}
                {props.config.form && (
                  <div class={[e('body', 'form')]}>
                    <wui-form
                      ref={formRef}
                      model={model}
                      label-position='left'
                      label-suffix=':'
                      labelWidth='100px'>
                      {formColumns.value.map((item: FormField) => renderFormItem(item))}
                    </wui-form>
                  </div>
                )}

                {/* 表格部分 */}
                {props.config.table && (
                  <div class={[e('body', 'table'), 'cfg-setup_table']}>
                    <wui-table
                      onRow-contextmenu={handleRowMenu}
                      border
                      height={props.config.table.height || '100%'}
                      data={model.table}>
                      {{
                        default: () => (
                          <>
                            {tableColumns.value.map((item: TableColumn) => renderTableColumn(item))}
                            {showTableOperations.value && (
                              <wui-table-column width='100px' align='center' label='Op'>
                                {{
                                  default: ({ row, $index }: any) => (
                                    <TableTool.Op
                                      flag={row.flag}
                                      onOp={op => handleOp(op, row, $index)}
                                    />
                                  )
                                }}
                              </wui-table-column>
                            )}
                          </>
                        ),
                        empty: () => <TableTool.Empty />
                      }}
                    </wui-table>
                  </div>
                )}
              </div>
            </WuiScrollbar>
          </MyDialog>

          {/* 渲染嵌套弹窗 */}
          {Object.entries(nestedDialogManager.dialogs).map(([key, dialogState]) =>
            dialogState.visible ? (
              <DynamicDialog
                key={key}
                v-model:visible={dialogState.visible}
                config={{
                  ...dialogState.config,
                  context: {
                    ...(props.config.context || {}),
                    ...(dialogState.config.context || {})
                  }
                }}
                apiCallbacks={props.apiCallbacks}
                events={props.events}
                parentNestedPath={currentNestedPath.value}
                dialogKey={key}
                onUpdate:visible={(val: boolean) => {
                  if (!val) {
                    nestedDialogManager.closeDialog(key)
                  }
                }}
              />
            ) : null
          )}
        </>
      )
    }
  }
})

export default DynamicDialog
